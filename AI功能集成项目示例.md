# 🎯 AI功能集成项目示例

## 📋 项目概述

本示例展示如何在实际前端项目中集成多种AI功能，包括智能搜索、个性化推荐、语音交互等，提升用户体验和开发效率。

## 🏗️ 项目架构

```
ai-enhanced-app/
├── src/
│   ├── components/
│   │   ├── ai/                    # AI功能组件
│   │   │   ├── SmartSearch/       # 智能搜索
│   │   │   ├── VoiceAssistant/    # 语音助手
│   │   │   ├── PersonalizedFeed/  # 个性化推荐
│   │   │   └── AIChat/            # AI聊天
│   │   ├── common/                # 通用组件
│   │   └── layout/                # 布局组件
│   ├── services/
│   │   ├── ai/                    # AI服务
│   │   │   ├── openai.js          # OpenAI集成
│   │   │   ├── tensorflow.js      # TensorFlow.js
│   │   │   └── speech.js          # 语音服务
│   │   └── api/                   # API服务
│   ├── hooks/                     # 自定义Hooks
│   ├── utils/                     # 工具函数
│   └── types/                     # TypeScript类型
├── public/
│   └── models/                    # AI模型文件
└── docs/                          # 文档
```

## 🔍 1. 智能搜索组件

### 核心功能
- 自然语言查询理解
- 实时搜索建议
- 语义搜索
- 搜索结果排序优化

### 实现代码

```typescript
// src/components/ai/SmartSearch/SmartSearch.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { useDebounce } from '../../../hooks/useDebounce';
import { searchService } from '../../../services/ai/search';
import { SearchResult, SearchSuggestion } from '../../../types/search';

interface SmartSearchProps {
  placeholder?: string;
  onResultSelect?: (result: SearchResult) => void;
}

export const SmartSearch: React.FC<SmartSearchProps> = ({
  placeholder = "智能搜索...",
  onResultSelect
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const debouncedQuery = useDebounce(query, 300);

  const performSearch = useCallback(async (searchQuery: string) => {
    if (searchQuery.length < 2) {
      setResults([]);
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      // AI驱动的搜索
      const searchResults = await searchService.intelligentSearch({
        query: searchQuery,
        includeSemanticSearch: true,
        maxResults: 10
      });

      setResults(searchResults.results);
      setSuggestions(searchResults.suggestions);
    } catch (error) {
      console.error('搜索失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (debouncedQuery) {
      performSearch(debouncedQuery);
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [debouncedQuery, performSearch]);

  const handleResultClick = (result: SearchResult) => {
    setQuery(result.title);
    setIsOpen(false);
    onResultSelect?.(result);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    performSearch(suggestion.text);
  };

  return (
    <div className="smart-search">
      <div className="search-input-container">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={placeholder}
          className="search-input"
          onFocus={() => query && setIsOpen(true)}
        />
        {isLoading && <div className="search-loading">🔍</div>}
      </div>

      {isOpen && (
        <div className="search-dropdown">
          {/* 搜索建议 */}
          {suggestions.length > 0 && (
            <div className="search-suggestions">
              <h4>搜索建议</h4>
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <span className="suggestion-icon">💡</span>
                  {suggestion.text}
                  <span className="suggestion-confidence">
                    {Math.round(suggestion.confidence * 100)}%
                  </span>
                </button>
              ))}
            </div>
          )}

          {/* 搜索结果 */}
          {results.length > 0 && (
            <div className="search-results">
              <h4>搜索结果</h4>
              {results.map((result) => (
                <div
                  key={result.id}
                  className="result-item"
                  onClick={() => handleResultClick(result)}
                >
                  <div className="result-header">
                    <h5>{result.title}</h5>
                    <span className="result-score">
                      {Math.round(result.relevanceScore * 100)}%
                    </span>
                  </div>
                  <p className="result-summary">{result.summary}</p>
                  <div className="result-tags">
                    {result.tags.map((tag, index) => (
                      <span key={index} className="tag">{tag}</span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
```

### 搜索服务实现

```typescript
// src/services/ai/search.ts
import OpenAI from 'openai';

class SearchService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.REACT_APP_OPENAI_API_KEY,
      dangerouslyAllowBrowser: true
    });
  }

  async intelligentSearch(params: {
    query: string;
    includeSemanticSearch: boolean;
    maxResults: number;
  }) {
    const { query, includeSemanticSearch, maxResults } = params;

    try {
      // 1. 查询意图分析
      const intent = await this.analyzeSearchIntent(query);
      
      // 2. 生成搜索建议
      const suggestions = await this.generateSuggestions(query, intent);
      
      // 3. 执行搜索
      let results = await this.performBasicSearch(query);
      
      // 4. 语义搜索增强
      if (includeSemanticSearch) {
        const semanticResults = await this.performSemanticSearch(query);
        results = this.mergeAndRankResults(results, semanticResults);
      }
      
      // 5. 结果排序和过滤
      results = this.rankResults(results, intent).slice(0, maxResults);

      return {
        results,
        suggestions,
        intent,
        totalCount: results.length
      };
    } catch (error) {
      console.error('智能搜索失败:', error);
      throw error;
    }
  }

  private async analyzeSearchIntent(query: string) {
    const response = await this.openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "分析用户搜索意图，返回JSON格式：{\"intent\": \"信息查找|购买|比较|学习\", \"category\": \"类别\", \"keywords\": [\"关键词\"]}"
        },
        {
          role: "user",
          content: `分析这个搜索查询的意图: "${query}"`
        }
      ],
      temperature: 0.3
    });

    try {
      return JSON.parse(response.choices[0].message.content || '{}');
    } catch {
      return { intent: '信息查找', category: '通用', keywords: [query] };
    }
  }

  private async generateSuggestions(query: string, intent: any) {
    const response = await this.openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "基于用户查询生成3-5个相关的搜索建议，返回JSON数组格式"
        },
        {
          role: "user",
          content: `为查询"${query}"生成搜索建议，意图是${intent.intent}`
        }
      ],
      temperature: 0.7
    });

    try {
      const suggestions = JSON.parse(response.choices[0].message.content || '[]');
      return suggestions.map((text: string, index: number) => ({
        text,
        confidence: 0.9 - index * 0.1,
        type: 'ai_generated'
      }));
    } catch {
      return [];
    }
  }

  private async performBasicSearch(query: string) {
    // 模拟基础搜索 - 实际项目中连接真实搜索API
    const mockResults = [
      {
        id: '1',
        title: `关于 "${query}" 的搜索结果`,
        summary: '这是一个模拟的搜索结果摘要...',
        url: '/result/1',
        relevanceScore: 0.95,
        tags: ['相关', '热门'],
        type: 'article'
      }
    ];
    
    return mockResults;
  }

  private async performSemanticSearch(query: string) {
    // 使用向量搜索进行语义匹配
    // 实际实现中会使用向量数据库如Pinecone、Weaviate等
    return [];
  }

  private mergeAndRankResults(basicResults: any[], semanticResults: any[]) {
    // 合并和排序搜索结果
    return [...basicResults, ...semanticResults]
      .sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  private rankResults(results: any[], intent: any) {
    // 基于意图调整结果排序
    return results.map(result => ({
      ...result,
      relevanceScore: this.calculateRelevanceScore(result, intent)
    })).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  private calculateRelevanceScore(result: any, intent: any) {
    let score = result.relevanceScore || 0.5;
    
    // 根据意图调整分数
    if (intent.intent === '购买' && result.type === 'product') {
      score += 0.2;
    } else if (intent.intent === '学习' && result.type === 'tutorial') {
      score += 0.2;
    }
    
    return Math.min(score, 1.0);
  }
}

export const searchService = new SearchService();
```

## 🎤 2. 语音助手组件

### 核心功能
- 语音识别和合成
- 自然语言理解
- 语音命令执行
- 多轮对话支持

### 实现代码

```typescript
// src/components/ai/VoiceAssistant/VoiceAssistant.tsx
import React, { useState, useEffect, useRef } from 'react';
import { useSpeechRecognition } from '../../../hooks/useSpeechRecognition';
import { useSpeechSynthesis } from '../../../hooks/useSpeechSynthesis';
import { voiceService } from '../../../services/ai/voice';

export const VoiceAssistant: React.FC = () => {
  const [isListening, setIsListening] = useState(false);
  const [conversation, setConversation] = useState<Array<{
    type: 'user' | 'assistant';
    text: string;
    timestamp: Date;
  }>>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    transcript,
    isListening: speechIsListening,
    startListening,
    stopListening,
    resetTranscript
  } = useSpeechRecognition();

  const { speak, isSpeaking } = useSpeechSynthesis();

  const conversationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (transcript && !speechIsListening) {
      handleUserInput(transcript);
      resetTranscript();
    }
  }, [transcript, speechIsListening]);

  useEffect(() => {
    // 自动滚动到最新消息
    if (conversationRef.current) {
      conversationRef.current.scrollTop = conversationRef.current.scrollHeight;
    }
  }, [conversation]);

  const handleUserInput = async (input: string) => {
    if (!input.trim()) return;

    // 添加用户消息
    const userMessage = {
      type: 'user' as const,
      text: input,
      timestamp: new Date()
    };
    setConversation(prev => [...prev, userMessage]);

    setIsProcessing(true);
    try {
      // AI处理用户输入
      const response = await voiceService.processVoiceCommand(input);
      
      // 添加助手回复
      const assistantMessage = {
        type: 'assistant' as const,
        text: response.text,
        timestamp: new Date()
      };
      setConversation(prev => [...prev, assistantMessage]);

      // 语音播放回复
      if (response.shouldSpeak) {
        speak(response.text);
      }

      // 执行命令
      if (response.action) {
        await executeAction(response.action);
      }
    } catch (error) {
      console.error('语音处理失败:', error);
      const errorMessage = {
        type: 'assistant' as const,
        text: '抱歉，我没有理解您的意思，请再试一次。',
        timestamp: new Date()
      };
      setConversation(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  const executeAction = async (action: any) => {
    switch (action.type) {
      case 'search':
        // 执行搜索
        window.location.href = `/search?q=${encodeURIComponent(action.query)}`;
        break;
      case 'navigate':
        // 页面导航
        window.location.href = action.url;
        break;
      case 'toggle_theme':
        // 切换主题
        document.body.classList.toggle('dark-theme');
        break;
      default:
        console.log('未知操作:', action);
    }
  };

  const toggleListening = () => {
    if (isListening) {
      stopListening();
      setIsListening(false);
    } else {
      startListening();
      setIsListening(true);
    }
  };

  return (
    <div className="voice-assistant">
      <div className="assistant-header">
        <h3>🎤 AI语音助手</h3>
        <button
          className={`voice-button ${isListening ? 'listening' : ''}`}
          onClick={toggleListening}
          disabled={isProcessing || isSpeaking}
        >
          {isListening ? '🔴 停止' : '🎤 开始'}
        </button>
      </div>

      <div className="conversation" ref={conversationRef}>
        {conversation.map((message, index) => (
          <div
            key={index}
            className={`message ${message.type}`}
          >
            <div className="message-content">
              <span className="message-text">{message.text}</span>
              <span className="message-time">
                {message.timestamp.toLocaleTimeString()}
              </span>
            </div>
          </div>
        ))}
        
        {isProcessing && (
          <div className="message assistant">
            <div className="message-content">
              <span className="message-text">正在思考...</span>
            </div>
          </div>
        )}
      </div>

      {isListening && (
        <div className="listening-indicator">
          <div className="pulse-animation"></div>
          <span>正在聆听...</span>
        </div>
      )}
    </div>
  );
};
```

### 语音服务实现

```typescript
// src/services/ai/voice.ts
import OpenAI from 'openai';

class VoiceService {
  private openai: OpenAI;
  private conversationHistory: Array<{role: string; content: string}> = [];

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.REACT_APP_OPENAI_API_KEY,
      dangerouslyAllowBrowser: true
    });
  }

  async processVoiceCommand(input: string) {
    try {
      // 添加到对话历史
      this.conversationHistory.push({
        role: 'user',
        content: input
      });

      // 保持对话历史在合理长度
      if (this.conversationHistory.length > 10) {
        this.conversationHistory = this.conversationHistory.slice(-8);
      }

      const response = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `你是一个智能语音助手。请分析用户的语音输入并返回JSON格式的响应：
            {
              "text": "回复文本",
              "shouldSpeak": true/false,
              "action": {
                "type": "search|navigate|toggle_theme|none",
                "query": "搜索查询",
                "url": "导航URL"
              }
            }
            
            支持的命令类型：
            - 搜索相关：搜索、查找、找一下
            - 导航相关：打开、跳转到、去
            - 主题切换：切换主题、暗色模式、亮色模式
            - 普通对话：其他日常对话`
          },
          ...this.conversationHistory
        ],
        temperature: 0.7
      });

      const assistantResponse = response.choices[0].message.content;
      
      // 添加助手回复到历史
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantResponse || ''
      });

      try {
        return JSON.parse(assistantResponse || '{}');
      } catch {
        return {
          text: assistantResponse || '我没有理解您的意思。',
          shouldSpeak: true,
          action: null
        };
      }
    } catch (error) {
      console.error('语音命令处理失败:', error);
      return {
        text: '抱歉，处理您的请求时出现了错误。',
        shouldSpeak: true,
        action: null
      };
    }
  }

  clearHistory() {
    this.conversationHistory = [];
  }
}

export const voiceService = new VoiceService();
```

## 📊 3. 个性化推荐组件

### 核心功能
- 用户行为分析
- 内容推荐算法
- 实时推荐更新
- A/B测试支持

### 实现代码

```typescript
// src/components/ai/PersonalizedFeed/PersonalizedFeed.tsx
import React, { useState, useEffect } from 'react';
import { recommendationService } from '../../../services/ai/recommendation';
import { useUserBehavior } from '../../../hooks/useUserBehavior';

interface RecommendationItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  category: string;
  score: number;
  reason: string;
}

export const PersonalizedFeed: React.FC = () => {
  const [recommendations, setRecommendations] = useState<RecommendationItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);

  const { trackInteraction, userProfile } = useUserBehavior();

  useEffect(() => {
    loadRecommendations();
  }, [refreshKey, userProfile]);

  const loadRecommendations = async () => {
    setIsLoading(true);
    try {
      const items = await recommendationService.getPersonalizedRecommendations({
        userId: userProfile.id,
        count: 10,
        categories: userProfile.preferences?.categories || [],
        excludeViewed: true
      });
      setRecommendations(items);
    } catch (error) {
      console.error('加载推荐失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleItemClick = (item: RecommendationItem) => {
    // 跟踪用户交互
    trackInteraction({
      type: 'click',
      itemId: item.id,
      category: item.category,
      score: item.score,
      timestamp: new Date()
    });

    // 导航到详情页
    window.location.href = `/item/${item.id}`;
  };

  const handleItemLike = (item: RecommendationItem) => {
    trackInteraction({
      type: 'like',
      itemId: item.id,
      category: item.category,
      score: item.score,
      timestamp: new Date()
    });

    // 更新推荐
    setRefreshKey(prev => prev + 1);
  };

  const handleItemDislike = (item: RecommendationItem) => {
    trackInteraction({
      type: 'dislike',
      itemId: item.id,
      category: item.category,
      score: item.score,
      timestamp: new Date()
    });

    // 从列表中移除
    setRecommendations(prev => prev.filter(r => r.id !== item.id));
  };

  if (isLoading) {
    return (
      <div className="personalized-feed loading">
        <div className="loading-skeleton">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="skeleton-item"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="personalized-feed">
      <div className="feed-header">
        <h2>🎯 为您推荐</h2>
        <button 
          className="refresh-button"
          onClick={() => setRefreshKey(prev => prev + 1)}
        >
          🔄 刷新
        </button>
      </div>

      <div className="recommendations-grid">
        {recommendations.map((item) => (
          <div key={item.id} className="recommendation-card">
            <div className="card-image">
              <img 
                src={item.imageUrl} 
                alt={item.title}
                onClick={() => handleItemClick(item)}
              />
            </div>
            
            <div className="card-content">
              <h3 className="card-title" onClick={() => handleItemClick(item)}>
                {item.title}
              </h3>
              <p className="card-description">{item.description}</p>
              
              <div className="card-meta">
                <span className="category">{item.category}</span>
                <span className="score">匹配度: {Math.round(item.score * 100)}%</span>
              </div>
              
              <div className="recommendation-reason">
                <span className="reason-icon">💡</span>
                <span className="reason-text">{item.reason}</span>
              </div>
            </div>
            
            <div className="card-actions">
              <button 
                className="action-button like"
                onClick={() => handleItemLike(item)}
              >
                👍 喜欢
              </button>
              <button 
                className="action-button dislike"
                onClick={() => handleItemDislike(item)}
              >
                👎 不感兴趣
              </button>
            </div>
          </div>
        ))}
      </div>

      {recommendations.length === 0 && (
        <div className="empty-state">
          <p>暂无推荐内容，请多浏览一些内容来改善推荐效果。</p>
        </div>
      )}
    </div>
  );
};
```

这个项目示例展示了如何在实际前端应用中集成多种AI功能，提供了完整的代码实现和最佳实践。你可以根据自己的项目需求选择性地实现这些功能。
