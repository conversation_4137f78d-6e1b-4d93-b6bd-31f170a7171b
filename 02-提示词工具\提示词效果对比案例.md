# 🔍 Vue/Nuxt AI提示词效果对比案例

## 📋 实际效果对比和优化技巧

通过真实的对比案例，展示如何优化Vue/Nuxt AI提示词以获得更好的代码生成效果。

## 💻 Vue组件生成效果对比

### 案例1：用户资料卡片组件

#### ❌ 优化前的提示词
```
创建一个用户资料组件
```

#### 生成结果（质量评分：3/10）
```vue
<template>
  <div>
    <h1>用户资料</h1>
    <p>姓名：{{ name }}</p>
    <p>邮箱：{{ email }}</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      name: '',
      email: ''
    }
  }
}
</script>
```

**问题分析**：
- 使用了Vue 2语法
- 缺少TypeScript支持
- 没有响应式设计
- 缺少交互功能

#### ✅ 优化后的提示词
```
作为Vue 3专家，请创建一个用户资料卡片组件：

**功能需求：**
- 显示用户头像、姓名、邮箱、角色
- 支持编辑模式切换
- 表单验证和错误提示
- 加载状态和保存功能

**技术要求：**
- 使用Vue 3 Composition API（<script setup>语法）
- 使用TypeScript进行类型定义
- 支持v-model双向绑定
- 包含适当的生命周期钩子

**样式要求：**
- 使用Tailwind CSS
- 响应式设计
- 支持暗色模式
- 遵循无障碍性标准

**Props设计：**
- user: User（用户对象）
- editable: boolean（是否可编辑）
- loading: boolean（加载状态）

**Events设计：**
- update:user（用户信息更新）
- save（保存事件）
- cancel（取消编辑）

请确保代码符合Vue 3 Composition API最佳实践，具有良好的类型安全性和可维护性。
```

#### 生成结果（质量评分：9/10）
```vue
<template>
  <div class="user-profile-card bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md mx-auto">
    <!-- 头像部分 -->
    <div class="flex items-center mb-6">
      <div class="relative">
        <img
          :src="user.avatar || '/default-avatar.png'"
          :alt="`${user.name}的头像`"
          class="w-16 h-16 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
        />
        <button
          v-if="editable && isEditing"
          @click="handleAvatarUpload"
          class="absolute -bottom-1 -right-1 bg-blue-500 text-white rounded-full p-1 hover:bg-blue-600 transition-colors"
          aria-label="更换头像"
        >
          <Icon name="camera" class="w-3 h-3" />
        </button>
      </div>
      
      <div class="ml-4 flex-1">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ user.name }}
          </h2>
          <button
            v-if="editable"
            @click="toggleEdit"
            :disabled="loading"
            class="text-blue-500 hover:text-blue-600 disabled:opacity-50"
            :aria-label="isEditing ? '取消编辑' : '编辑资料'"
          >
            <Icon :name="isEditing ? 'x' : 'edit'" class="w-4 h-4" />
          </button>
        </div>
        <span class="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full mt-1">
          {{ user.role }}
        </span>
      </div>
    </div>

    <!-- 信息展示/编辑 -->
    <form v-if="isEditing" @submit.prevent="handleSave" class="space-y-4">
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          姓名
        </label>
        <input
          id="name"
          v-model="form.name"
          type="text"
          :class="[
            'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
            errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          ]"
          :disabled="loading"
          @blur="validateField('name')"
        />
        <p v-if="errors.name" class="mt-1 text-sm text-red-600">
          {{ errors.name }}
        </p>
      </div>

      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          邮箱
        </label>
        <input
          id="email"
          v-model="form.email"
          type="email"
          :class="[
            'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
            errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          ]"
          :disabled="loading"
          @blur="validateField('email')"
        />
        <p v-if="errors.email" class="mt-1 text-sm text-red-600">
          {{ errors.email }}
        </p>
      </div>

      <div class="flex space-x-3 pt-4">
        <button
          type="submit"
          :disabled="!isFormValid || loading"
          class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="loading" class="flex items-center justify-center">
            <Icon name="spinner" class="w-4 h-4 mr-2 animate-spin" />
            保存中...
          </span>
          <span v-else>保存</span>
        </button>
        <button
          type="button"
          @click="cancelEdit"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
        >
          取消
        </button>
      </div>
    </form>

    <!-- 只读模式 -->
    <div v-else class="space-y-3">
      <div class="flex items-center">
        <Icon name="user" class="w-4 h-4 text-gray-400 mr-2" />
        <span class="text-gray-900 dark:text-white">{{ user.name }}</span>
      </div>
      <div class="flex items-center">
        <Icon name="mail" class="w-4 h-4 text-gray-400 mr-2" />
        <span class="text-gray-600 dark:text-gray-300">{{ user.email }}</span>
      </div>
      <div class="flex items-center">
        <Icon name="calendar" class="w-4 h-4 text-gray-400 mr-2" />
        <span class="text-gray-600 dark:text-gray-300">
          加入于 {{ formatDate(user.createdAt) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: string
  createdAt: string
}

interface Props {
  user: User
  editable?: boolean
  loading?: boolean
}

interface Emits {
  'update:user': [user: User]
  'save': [user: User]
  'cancel': []
}

const props = withDefaults(defineProps<Props>(), {
  editable: false,
  loading: false
})

const emit = defineEmits<Emits>()

// 编辑状态管理
const isEditing = ref(false)
const form = ref<Partial<User>>({})
const errors = ref<Record<string, string>>({})

// 表单验证
const isFormValid = computed(() => {
  return form.value.name && 
         form.value.email && 
         !Object.keys(errors.value).length
})

// 验证单个字段
const validateField = (field: string) => {
  errors.value = { ...errors.value }
  
  switch (field) {
    case 'name':
      if (!form.value.name?.trim()) {
        errors.value.name = '姓名不能为空'
      } else if (form.value.name.length < 2) {
        errors.value.name = '姓名至少需要2个字符'
      } else {
        delete errors.value.name
      }
      break
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!form.value.email?.trim()) {
        errors.value.email = '邮箱不能为空'
      } else if (!emailRegex.test(form.value.email)) {
        errors.value.email = '请输入有效的邮箱地址'
      } else {
        delete errors.value.email
      }
      break
  }
}

// 切换编辑模式
const toggleEdit = () => {
  if (isEditing.value) {
    cancelEdit()
  } else {
    startEdit()
  }
}

const startEdit = () => {
  isEditing.value = true
  form.value = { ...props.user }
  errors.value = {}
}

const cancelEdit = () => {
  isEditing.value = false
  form.value = {}
  errors.value = {}
  emit('cancel')
}

// 保存处理
const handleSave = async () => {
  // 验证所有字段
  validateField('name')
  validateField('email')
  
  if (!isFormValid.value) return

  const updatedUser = { ...props.user, ...form.value } as User
  emit('update:user', updatedUser)
  emit('save', updatedUser)
  isEditing.value = false
}

// 头像上传
const handleAvatarUpload = () => {
  // 实现头像上传逻辑
  console.log('上传头像')
}

// 日期格式化
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 监听用户数据变化
watch(() => props.user, (newUser) => {
  if (!isEditing.value) {
    form.value = { ...newUser }
  }
}, { immediate: true })
</script>
```

**改进效果**：
- ✅ 使用Vue 3 Composition API和TypeScript
- ✅ 完整的表单验证和错误处理
- ✅ 响应式设计和暗色模式支持
- ✅ 无障碍性标准遵循
- ✅ 加载状态和用户体验优化
- ✅ 清晰的Props和Events设计

## 🔧 Nuxt页面生成效果对比

### 案例2：产品详情页

#### ❌ 优化前的提示词
```
创建一个产品页面
```

#### 生成结果（质量评分：2/10）
```vue
<template>
  <div>
    <h1>产品详情</h1>
    <p>{{ product.name }}</p>
    <p>{{ product.price }}</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      product: {}
    }
  }
}
</script>
```

#### ✅ 优化后的提示词
```
作为Nuxt 3专家，请创建一个电商产品详情页面：

**页面信息：**
- 路由路径：/products/[slug]
- 页面类型：SSR（SEO优化）
- 数据获取：useFetch预取产品数据

**功能需求：**
- 产品图片轮播和缩放
- 规格选择（颜色、尺寸等）
- 数量选择和购物车添加
- 相关产品推荐
- 用户评价展示
- 社交分享功能

**SEO要求：**
- 动态Title和Description
- Open Graph标签
- 结构化数据（Product Schema）
- 面包屑导航

**性能要求：**
- 图片懒加载和优化
- 关键CSS内联
- 预加载相关资源

**错误处理：**
- 产品不存在时显示404
- 加载失败时的降级方案
- 网络错误重试机制

请确保页面符合Nuxt 3最佳实践，具有良好的SEO和性能表现。
```

#### 生成结果（质量评分：9/10）
```vue
<template>
  <div class="product-detail-page">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb mb-6" aria-label="面包屑导航">
      <ol class="flex items-center space-x-2 text-sm text-gray-600">
        <li><NuxtLink to="/" class="hover:text-blue-600">首页</NuxtLink></li>
        <li><Icon name="chevron-right" class="w-4 h-4" /></li>
        <li><NuxtLink :to="`/categories/${product.category.slug}`" class="hover:text-blue-600">
          {{ product.category.name }}
        </NuxtLink></li>
        <li><Icon name="chevron-right" class="w-4 h-4" /></li>
        <li class="text-gray-900">{{ product.name }}</li>
      </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
      <!-- 产品图片 -->
      <div class="product-images">
        <div class="main-image mb-4">
          <NuxtImg
            :src="selectedImage"
            :alt="product.name"
            class="w-full h-96 object-cover rounded-lg"
            loading="eager"
            :placeholder="[400, 400, 10]"
          />
        </div>
        <div class="thumbnail-grid grid grid-cols-4 gap-2">
          <button
            v-for="(image, index) in product.images"
            :key="index"
            @click="selectedImage = image.url"
            :class="[
              'border-2 rounded-md overflow-hidden',
              selectedImage === image.url ? 'border-blue-500' : 'border-gray-200'
            ]"
          >
            <NuxtImg
              :src="image.thumbnail"
              :alt="`${product.name} 图片 ${index + 1}`"
              class="w-full h-20 object-cover"
              loading="lazy"
            />
          </button>
        </div>
      </div>

      <!-- 产品信息 -->
      <div class="product-info">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ product.name }}</h1>
        
        <!-- 价格 -->
        <div class="price-section mb-6">
          <div class="flex items-center space-x-4">
            <span class="text-3xl font-bold text-red-600">
              ¥{{ formatPrice(currentPrice) }}
            </span>
            <span v-if="product.originalPrice > currentPrice" class="text-lg text-gray-500 line-through">
              ¥{{ formatPrice(product.originalPrice) }}
            </span>
            <span v-if="discountPercentage > 0" class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
              省{{ discountPercentage }}%
            </span>
          </div>
        </div>

        <!-- 规格选择 -->
        <div class="specifications mb-6">
          <div v-for="spec in product.specifications" :key="spec.name" class="mb-4">
            <h3 class="text-sm font-medium text-gray-900 mb-2">{{ spec.name }}</h3>
            <div class="flex flex-wrap gap-2">
              <button
                v-for="option in spec.options"
                :key="option.value"
                @click="selectSpecification(spec.name, option.value)"
                :disabled="!option.available"
                :class="[
                  'px-4 py-2 border rounded-md text-sm transition-colors',
                  selectedSpecs[spec.name] === option.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 hover:border-gray-400',
                  !option.available && 'opacity-50 cursor-not-allowed'
                ]"
              >
                {{ option.label }}
              </button>
            </div>
          </div>
        </div>

        <!-- 数量选择 -->
        <div class="quantity-section mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-2">数量</h3>
          <div class="flex items-center space-x-4">
            <div class="flex items-center border border-gray-300 rounded-md">
              <button
                @click="decreaseQuantity"
                :disabled="quantity <= 1"
                class="px-3 py-2 hover:bg-gray-100 disabled:opacity-50"
              >
                <Icon name="minus" class="w-4 h-4" />
              </button>
              <input
                v-model.number="quantity"
                type="number"
                min="1"
                :max="product.stock"
                class="w-16 text-center border-0 focus:ring-0"
              />
              <button
                @click="increaseQuantity"
                :disabled="quantity >= product.stock"
                class="px-3 py-2 hover:bg-gray-100 disabled:opacity-50"
              >
                <Icon name="plus" class="w-4 h-4" />
              </button>
            </div>
            <span class="text-sm text-gray-600">库存：{{ product.stock }}件</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions space-y-3 mb-8">
          <button
            @click="addToCart"
            :disabled="!canAddToCart"
            class="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="addingToCart" class="flex items-center justify-center">
              <Icon name="spinner" class="w-4 h-4 mr-2 animate-spin" />
              添加中...
            </span>
            <span v-else>加入购物车</span>
          </button>
          
          <button
            @click="buyNow"
            :disabled="!canAddToCart"
            class="w-full bg-orange-600 text-white py-3 px-6 rounded-md hover:bg-orange-700 disabled:opacity-50 transition-colors"
          >
            立即购买
          </button>
        </div>

        <!-- 产品特性 -->
        <div class="product-features">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="flex items-center">
              <Icon name="truck" class="w-4 h-4 mr-2 text-green-600" />
              <span>免费配送</span>
            </div>
            <div class="flex items-center">
              <Icon name="shield" class="w-4 h-4 mr-2 text-blue-600" />
              <span>正品保证</span>
            </div>
            <div class="flex items-center">
              <Icon name="refresh" class="w-4 h-4 mr-2 text-purple-600" />
              <span>7天无理由退货</span>
            </div>
            <div class="flex items-center">
              <Icon name="headphones" class="w-4 h-4 mr-2 text-orange-600" />
              <span>24小时客服</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品详情标签页 -->
    <div class="product-tabs">
      <div class="border-b border-gray-200 mb-6">
        <nav class="flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            ]"
          >
            {{ tab.label }}
          </button>
        </nav>
      </div>

      <div class="tab-content">
        <div v-if="activeTab === 'description'" class="prose max-w-none">
          <div v-html="product.description"></div>
        </div>
        
        <div v-if="activeTab === 'specifications'" class="specifications-table">
          <table class="w-full border-collapse border border-gray-300">
            <tbody>
              <tr v-for="spec in product.detailedSpecs" :key="spec.name" class="border-b">
                <td class="border border-gray-300 px-4 py-2 bg-gray-50 font-medium">{{ spec.name }}</td>
                <td class="border border-gray-300 px-4 py-2">{{ spec.value }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div v-if="activeTab === 'reviews'">
          <ProductReviews :product-id="product.id" />
        </div>
      </div>
    </div>

    <!-- 相关产品推荐 -->
    <div class="related-products mt-12">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">相关产品</h2>
      <ProductGrid :products="relatedProducts" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Product {
  id: string
  name: string
  slug: string
  price: number
  originalPrice: number
  stock: number
  images: Array<{ url: string; thumbnail: string }>
  category: { name: string; slug: string }
  specifications: Array<{
    name: string
    options: Array<{ label: string; value: string; available: boolean }>
  }>
  description: string
  detailedSpecs: Array<{ name: string; value: string }>
}

// 路由参数
const route = useRoute()
const productSlug = route.params.slug as string

// 数据获取
const { data: product, error } = await useFetch<Product>(`/api/products/${productSlug}`, {
  key: `product-${productSlug}`,
  transform: (data: any) => ({
    ...data,
    images: data.images || [{ url: '/placeholder.jpg', thumbnail: '/placeholder-thumb.jpg' }]
  })
})

// 错误处理
if (error.value) {
  throw createError({
    statusCode: 404,
    statusMessage: '产品不存在'
  })
}

// 相关产品
const { data: relatedProducts } = await useFetch<Product[]>(`/api/products/${product.value.id}/related`, {
  server: false
})

// SEO设置
useSeoMeta({
  title: `${product.value.name} - 商城`,
  description: `${product.value.name}，现价¥${product.value.price}。${product.value.description?.substring(0, 100)}...`,
  ogTitle: product.value.name,
  ogDescription: product.value.description,
  ogImage: product.value.images[0]?.url,
  ogType: 'product'
})

// 结构化数据
useJsonld({
  '@context': 'https://schema.org',
  '@type': 'Product',
  name: product.value.name,
  description: product.value.description,
  image: product.value.images.map(img => img.url),
  offers: {
    '@type': 'Offer',
    price: product.value.price,
    priceCurrency: 'CNY',
    availability: product.value.stock > 0 ? 'InStock' : 'OutOfStock'
  }
})

// 响应式状态
const selectedImage = ref(product.value.images[0]?.url)
const selectedSpecs = ref<Record<string, string>>({})
const quantity = ref(1)
const activeTab = ref('description')
const addingToCart = ref(false)

// 计算属性
const currentPrice = computed(() => {
  // 根据选择的规格计算价格
  return product.value.price
})

const discountPercentage = computed(() => {
  if (product.value.originalPrice > currentPrice.value) {
    return Math.round((1 - currentPrice.value / product.value.originalPrice) * 100)
  }
  return 0
})

const canAddToCart = computed(() => {
  return product.value.stock > 0 && quantity.value <= product.value.stock
})

// 标签页配置
const tabs = [
  { id: 'description', label: '产品详情' },
  { id: 'specifications', label: '规格参数' },
  { id: 'reviews', label: '用户评价' }
]

// 方法
const selectSpecification = (specName: string, value: string) => {
  selectedSpecs.value[specName] = value
}

const increaseQuantity = () => {
  if (quantity.value < product.value.stock) {
    quantity.value++
  }
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const addToCart = async () => {
  addingToCart.value = true
  try {
    await $fetch('/api/cart/add', {
      method: 'POST',
      body: {
        productId: product.value.id,
        quantity: quantity.value,
        specifications: selectedSpecs.value
      }
    })
    
    // 显示成功提示
    useToast().success('已添加到购物车')
  } catch (error) {
    useToast().error('添加失败，请重试')
  } finally {
    addingToCart.value = false
  }
}

const buyNow = async () => {
  await addToCart()
  await navigateTo('/checkout')
}

const formatPrice = (price: number) => {
  return price.toFixed(2)
}
</script>
```

## 📊 效果评估标准

### 代码质量评分（1-10分）
- **功能完整性**（30%）：是否实现了所有要求的功能
- **代码规范性**（25%）：是否遵循Vue/Nuxt最佳实践
- **类型安全性**（20%）：TypeScript类型定义的完整性
- **用户体验**（15%）：交互设计和无障碍性
- **性能优化**（10%）：代码性能和优化程度

### 提示词优化技巧总结

#### 1. **明确技术栈**
- 明确指定Vue 3 + Nuxt 3 + TypeScript
- 强调使用Composition API和`<script setup>`语法
- 指定具体的UI框架（如Tailwind CSS）

#### 2. **详细功能描述**
- 列出具体的功能需求
- 包含交互细节和边界情况
- 明确Props和Events设计

#### 3. **质量要求**
- 强调代码规范和最佳实践
- 要求类型安全和错误处理
- 包含无障碍性和性能考虑

#### 4. **上下文信息**
- 提供足够的业务背景
- 说明使用场景和约束条件
- 包含相关的技术决策依据

通过这些对比案例可以看出，详细和专业的提示词能够显著提升AI生成代码的质量，从简单的示例代码提升到可直接用于生产环境的企业级代码。
