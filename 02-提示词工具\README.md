# 🎯 提示词工具目录

## 📋 目录说明

本目录包含Vue/Nuxt AI开发的核心提示词工具和模板，帮助你高效地与AI工具协作，生成高质量的代码和解决方案。

## 🛠️ 工具列表

### 📚 核心模板库
- **Vue-Nuxt AI提示词模板库.md** - 7个专业提示词模板，完全适配Vue/Nuxt技术栈
- **提示词效果对比案例.md** - 实际效果对比和优化技巧示例

### 🎯 专用模板
- **Vue组件生成模板.md** - Vue 3 Composition API组件生成专用模板
- **Nuxt页面生成模板.md** - Nuxt 3页面和布局生成模板
- **Composables生成模板.md** - 可复用组合式函数生成模板
- **Pinia Store模板.md** - 状态管理Store生成模板

### 🔧 问题解决模板
- **Bug调试模板.md** - Vue/Nuxt特定的问题诊断和解决模板
- **性能优化模板.md** - 性能分析和优化建议模板

## 📖 模板分类

### 1. **代码生成类模板**
适用于日常开发中的代码生成需求：
- Vue 3 Composition API组件
- Nuxt 3页面和布局
- 自定义Composables
- Pinia状态管理

### 2. **问题解决类模板**
适用于开发过程中遇到的问题：
- Bug调试和分析
- 性能优化诊断
- 兼容性问题解决

### 3. **学习理解类模板**
适用于技术学习和概念理解：
- 技术概念深度学习
- 代码审查请求
- 最佳实践咨询

## 🎯 使用指南

### 快速开始
1. **选择合适的模板**：根据你的需求选择对应的提示词模板
2. **填写占位符**：将模板中的`{}`占位符替换为你的具体需求
3. **发送给AI**：将完整的提示词发送给ChatGPT、Claude或GitHub Copilot
4. **优化结果**：根据AI回复进行进一步的优化和调整

### 模板使用技巧
- **具体化描述**：用具体的数据和场景替换模糊的描述
- **提供上下文**：给AI足够的背景信息来理解你的需求
- **明确约束条件**：指定技术栈、格式要求等约束条件
- **迭代优化**：根据使用效果不断优化模板内容

### 效果评估
使用模板后，建议从以下维度评估效果：
- **代码质量**（1-10分）：功能正确性、代码规范性、可维护性
- **响应相关性**（1-10分）：是否理解需求、回复是否切题
- **实用性**（1-10分）：是否可直接使用、是否需要大量修改

## 🔗 相关目录

- **01-学习指南** - 学习如何使用这些提示词模板
- **03-工具配置** - 配置AI工具以更好地使用这些模板
- **04-项目实践** - 在实际项目中应用这些模板
- **05-企业级应用** - 企业级场景下的高级模板使用

## 📈 进阶使用

### 自定义模板
根据你的项目特点和团队需求，可以：
- 基于现有模板创建项目特定的模板
- 添加你常用的技术栈信息
- 建立团队共享的模板库

### 团队协作
- 分享有效的提示词模板给团队成员
- 建立团队标准化的提示词规范
- 收集团队反馈，持续优化模板质量

---

💡 **提示**：这些模板是基于Vue 3 + Nuxt 3 + TypeScript技术栈优化的，使用时请确保AI工具了解你的技术栈背景。
