# 🎯 Vue/Nuxt AI提示词模板库

## 📋 专为Vue/Nuxt开发者定制的提示词模板

基于Vue 3 + Nuxt 3 + TypeScript技术栈，提供高效的AI协作模板。

## 💻 Vue组件生成模板

### 1. Vue 3 Composition API组件生成
```
作为Vue 3专家，请创建一个{组件名称}组件：

**功能需求：**
- {功能1：如"支持用户输入和验证"}
- {功能2：如"响应式数据绑定"}
- {功能3：如"事件处理和状态管理"}

**技术要求：**
- 使用Vue 3 Composition API（<script setup>语法）
- 使用TypeScript进行类型定义
- 遵循Vue 3最佳实践
- 支持v-model双向绑定（如适用）
- 包含适当的生命周期钩子

**样式要求：**
- 使用{样式方案：如"Tailwind CSS"或"CSS Modules"}
- 响应式设计
- 支持{主题：如"暗色模式"}
- 遵循无障碍性标准

**Props和Events设计：**
- Props：{列出需要的props及其类型}
- Events：{列出需要触发的事件}
- Slots：{如果需要插槽，请说明}

**输出要求：**
1. 完整的Vue 3组件代码（.vue文件）
2. TypeScript接口定义
3. 组件使用示例
4. Props和Events文档
5. 详细的代码注释

请确保代码符合Vue 3 Composition API最佳实践，具有良好的类型安全性和可维护性。
```

### 2. Nuxt页面生成模板
```
作为Nuxt 3专家，请创建一个{页面功能描述}页面：

**页面信息：**
- 路由路径：{如"/products/[id]"}
- 页面类型：{如"SSR"、"SPA"、"SSG"}
- 数据获取方式：{如"useFetch"、"$fetch"}

**功能需求：**
- {功能1：如"动态路由参数处理"}
- {功能2：如"SEO优化和Meta标签"}
- {功能3：如"数据预取和缓存"}
- {功能4：如"错误处理和加载状态"}

**技术要求：**
- 使用Nuxt 3 Composition API
- TypeScript类型支持
- 适当的数据获取策略（server/client）
- SEO友好的Meta标签设置
- 错误边界和加载状态处理

**数据需求：**
- API端点：{如"/api/products/:id"}
- 数据结构：{描述期望的数据格式}
- 缓存策略：{如"key-based caching"}

**SEO要求：**
- 动态Title和Description
- Open Graph标签
- 结构化数据（JSON-LD）
- 适当的HTTP状态码

**输出要求：**
1. 完整的Nuxt页面代码（pages/目录结构）
2. 相关的TypeScript类型定义
3. SEO配置和Meta标签
4. 错误处理逻辑
5. 使用说明和最佳实践

请确保页面符合Nuxt 3最佳实践，具有良好的性能和SEO表现。
```

### 3. Composables生成模板
```
作为Vue/Nuxt Composables专家，请创建一个{Composable名称}：

**Composable功能：**
- 主要用途：{详细描述Composable的用途}
- 解决的问题：{说明它解决什么开发问题}
- 使用场景：{列出典型的使用场景}

**API设计：**
- 参数：
  - {参数1}：{类型} - {说明}
  - {参数2}：{类型} - {说明}
- 返回值：
  - {返回值1}：{类型} - {说明}
  - {返回值2}：{类型} - {说明}

**技术要求：**
- 使用Vue 3 Composition API
- 完整的TypeScript类型支持
- 适当的响应式处理（ref、reactive、computed）
- 生命周期管理（onMounted、onUnmounted等）
- 错误处理和边界情况

**特殊要求：**
- {要求1：如"支持SSR"}
- {要求2：如"可配置选项"}
- {要求3：如"内存泄漏防护"}

**Nuxt集成：**
- 是否需要Nuxt特定功能（如useFetch、useRuntimeConfig）
- 服务端渲染兼容性
- 自动导入配置

**输出要求：**
1. 完整的Composable实现代码
2. TypeScript类型定义和接口
3. 使用示例（至少3个不同场景）
4. 单元测试代码
5. JSDoc文档注释
6. 最佳实践说明

请确保Composable具有良好的可复用性、类型安全性和性能表现。
```

## 🏪 Pinia状态管理模板

### 4. Pinia Store生成模板
```
作为Pinia状态管理专家，请创建一个{Store名称}状态管理模块：

**Store职责：**
- 管理的数据：{如"用户信息、认证状态"}
- 主要功能：{如"登录、登出、权限验证"}
- 数据来源：{如"API接口、本地存储"}

**状态设计：**
- 状态字段：
  - {字段1}：{类型} - {说明}
  - {字段2}：{类型} - {说明}
- 计算属性：
  - {计算属性1}：{类型} - {说明}

**操作设计：**
- 同步操作：
  - {操作1}：{说明}
  - {操作2}：{说明}
- 异步操作：
  - {操作3}：{说明和API调用}
  - {操作4}：{说明和错误处理}

**技术要求：**
- 使用Pinia Composition API语法
- 完整的TypeScript类型支持
- 适当的错误处理和加载状态
- 数据持久化（如需要）
- SSR兼容性

**集成要求：**
- Nuxt 3集成配置
- 自动导入设置
- 开发工具支持
- 中间件集成（如需要）

**数据流：**
- API调用方式：{如"$fetch"、"useFetch"}
- 错误处理策略
- 缓存和更新机制
- 乐观更新支持

**输出要求：**
1. 完整的Pinia Store代码
2. TypeScript类型定义
3. 使用示例和最佳实践
4. 错误处理和边界情况
5. 单元测试代码
6. Nuxt插件配置（如需要）

请确保Store具有良好的可维护性、类型安全性和性能表现。
```

## 🔧 Nuxt配置和工具模板

### 5. Nuxt项目配置优化模板
```
作为Nuxt 3配置专家，请为{项目类型}项目优化Nuxt配置：

**项目特征：**
- 项目类型：{如"电商网站"、"管理后台"、"博客"}
- 预期规模：{如"中大型项目，100+页面"}
- 性能要求：{如"首屏加载<2s，SEO友好"}
- 部署环境：{如"Vercel"、"自建服务器"}

**功能需求：**
- SSR/SSG策略：{具体的渲染策略}
- 国际化支持：{如"多语言支持"}
- PWA功能：{如"离线支持、推送通知"}
- 分析和监控：{如"Google Analytics、错误监控"}

**性能优化：**
- 代码分割策略
- 图片优化配置
- 缓存策略设置
- 预加载和预取配置

**开发体验：**
- TypeScript严格模式
- ESLint和Prettier配置
- 开发工具集成
- 热重载优化

**第三方集成：**
- UI框架：{如"Tailwind CSS"、"Vuetify"}
- 状态管理：{如"Pinia"}
- 数据获取：{如"Apollo"、"Axios"}
- 其他模块：{列出需要的Nuxt模块}

**输出要求：**
1. 完整的nuxt.config.ts配置
2. 相关的配置文件（tsconfig.json、.eslintrc等）
3. package.json依赖配置
4. 环境变量配置示例
5. 部署配置文件
6. 性能优化说明
7. 开发和生产环境差异配置

请确保配置符合Nuxt 3最佳实践，具有良好的开发体验和生产性能。
```

## 🐛 问题解决模板

### 6. Vue/Nuxt Bug调试模板
```
作为Vue/Nuxt调试专家，请帮我解决以下问题：

**问题描述：**
{详细描述问题现象}

**环境信息：**
- Vue版本：{Vue 3.x.x}
- Nuxt版本：{Nuxt 3.x.x}
- Node.js版本：{Node版本}
- 包管理器：{npm/yarn/pnpm}
- 浏览器：{浏览器版本}
- 操作系统：{操作系统}

**技术栈详情：**
- UI框架：{如Tailwind CSS、Vuetify}
- 状态管理：{如Pinia}
- 其他关键依赖：{列出相关依赖}

**重现步骤：**
1. {具体操作步骤1}
2. {具体操作步骤2}
3. {具体操作步骤3}

**期望行为：** {描述期望的正确行为}
**实际行为：** {描述实际发生的错误行为}

**相关代码：**
```vue
{粘贴相关的Vue组件代码}
```

```typescript
{粘贴相关的TypeScript代码}
```

**错误信息：**
```
{粘贴控制台错误信息}
```

**Nuxt特定信息：**
- 渲染模式：{SSR/SPA/SSG}
- 是否在服务端出现：{是/否}
- 相关的Nuxt配置：{如有相关配置}

**已尝试的解决方案：**
- {尝试方案1及结果}
- {尝试方案2及结果}

**请提供：**
1. **根因分析**：问题的根本原因和Vue/Nuxt特定考虑
2. **解决方案**：详细的修复步骤和代码
3. **修复代码**：完整的修复后代码
4. **验证方法**：如何确认问题已解决
5. **预防措施**：避免类似问题的Vue/Nuxt最佳实践
6. **相关资源**：Vue/Nuxt官方文档或社区资源链接

请特别关注Vue 3 Composition API和Nuxt 3的特殊性，提供针对性的解决方案。
```

### 7. Vue/Nuxt性能优化模板
```
作为Vue/Nuxt性能优化专家，请帮我优化以下性能问题：

**性能问题描述：** {具体的性能问题}

**当前性能指标：**
- 首屏加载时间（FCP）：{时间}
- 最大内容绘制（LCP）：{时间}
- 首次输入延迟（FID）：{时间}
- 累积布局偏移（CLS）：{数值}
- Lighthouse评分：{分数}
- Bundle大小：{大小}

**技术栈信息：**
- Vue版本：{Vue 3.x.x}
- Nuxt版本：{Nuxt 3.x.x}
- 渲染模式：{SSR/SPA/SSG/Hybrid}
- UI框架：{如Tailwind CSS、Element Plus}
- 主要依赖：{列出大型依赖}

**页面特征：**
- 页面类型：{如"商品列表页"、"用户仪表板"}
- 数据量：{如"500+商品"、"复杂表格"}
- 交互复杂度：{如"实时搜索、无限滚动"}
- 媒体资源：{如"大量图片、视频"}

**Nuxt特定配置：**
```typescript
{粘贴当前的nuxt.config.ts相关配置}
```

**关键组件代码：**
```vue
{粘贴性能瓶颈组件代码}
```

**请分析并提供：**

1. **性能瓶颈识别**
   - Vue组件层面的问题
   - Nuxt配置层面的问题
   - 网络和资源加载问题

2. **Vue/Nuxt特定优化方案**
   - 组件懒加载和代码分割
   - SSR/SSG优化策略
   - 缓存策略（页面、API、静态资源）
   - 预取和预加载配置

3. **代码优化实现**
   - 优化后的组件代码
   - Nuxt配置调整
   - 性能监控代码

4. **效果预期**
   - 预期的性能提升数据
   - Core Web Vitals改善
   - 用户体验提升

5. **监控和测试**
   - 性能监控方案
   - A/B测试建议
   - 持续优化策略

6. **Nuxt最佳实践**
   - 渲染策略选择
   - 模块和插件优化
   - 部署优化建议

请确保优化方案充分利用Vue 3和Nuxt 3的性能特性，提供可量化的改进效果。
```

## 🎯 使用技巧

### Vue/Nuxt特定优化建议

1. **组件开发**
   - 明确指定使用Composition API
   - 强调TypeScript类型安全
   - 考虑SSR兼容性

2. **Nuxt页面**
   - 明确渲染策略（SSR/SPA/SSG）
   - 重视SEO和Meta标签
   - 考虑数据获取策略

3. **状态管理**
   - 优先使用Pinia
   - 考虑服务端状态同步
   - 重视类型安全

4. **性能优化**
   - 利用Nuxt的自动优化特性
   - 考虑代码分割和懒加载
   - 重视Core Web Vitals

这个Vue/Nuxt专用模板库为你提供了完整的技术栈支持，确保AI生成的代码符合Vue/Nuxt最佳实践。
