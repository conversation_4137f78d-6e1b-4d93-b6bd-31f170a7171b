# 🎯 前端开发AI提示词模板库

## 📋 使用说明

本模板库包含了前端开发中最常用的AI提示词模板，只需要替换`{}`中的占位符即可使用。建议收藏此文档，在日常开发中随时调用。

## 💻 代码生成类模板

### 1. React组件生成（通用）
```
作为React专家，请创建一个{组件名称}组件：

**功能需求：**
- {功能1：如"支持用户输入邮箱和密码"}
- {功能2：如"表单验证和错误提示"}
- {功能3：如"提交按钮和加载状态"}

**技术要求：**
- 使用TypeScript
- 使用React Hooks（useState, useEffect等）
- 遵循React最佳实践
- {其他要求：如"集成react-hook-form"}

**样式要求：**
- {样式方案：如"使用Tailwind CSS"}
- 响应式设计
- {主题：如"支持暗色模式"}

**输出要求：**
1. 完整的TypeScript组件代码
2. 相关的接口类型定义
3. CSS样式文件（如需要）
4. 使用示例和Props说明
5. 详细的代码注释

请确保代码具有良好的可读性、可维护性和可访问性。
```

### 2. 自定义Hook生成
```
作为React Hooks专家，请创建一个{Hook名称}自定义Hook：

**功能描述：** {详细描述Hook的用途}

**参数设计：**
- {参数1}：{类型} - {说明}
- {参数2}：{类型} - {说明}

**返回值设计：**
- {返回值1}：{类型} - {说明}
- {返回值2}：{类型} - {说明}

**特殊要求：**
- {要求1：如"支持依赖数组"}
- {要求2：如"包含清理逻辑"}
- {要求3：如"错误处理机制"}

**请提供：**
1. 完整的Hook实现代码
2. TypeScript类型定义
3. 使用示例（至少2个场景）
4. 单元测试代码
5. 性能优化说明

**技术栈：** React 18 + TypeScript
```

### 3. API服务层生成
```
作为前端架构师，请创建{API模块名称}的完整API服务层：

**API信息：**
- 基础URL：{API基础地址}
- 主要端点：
  - {端点1}：{方法} {路径} - {功能说明}
  - {端点2}：{方法} {路径} - {功能说明}
  - {端点3}：{方法} {路径} - {功能说明}

**数据模型：**
```typescript
{粘贴相关的TypeScript接口定义}
```

**技术要求：**
- 使用Axios进行HTTP请求
- 完整的TypeScript类型支持
- 统一的错误处理机制
- 请求/响应拦截器
- 自动重试机制
- 请求取消支持

**请提供：**
1. API服务类的完整实现
2. 错误处理工具函数
3. React Hook封装（如useQuery, useMutation）
4. 使用示例和最佳实践
5. 单元测试代码

**输出格式：**
- 模块化的代码结构
- 详细的JSDoc注释
- 使用文档和示例
```

### 4. 状态管理方案
```
作为状态管理专家，请为{应用场景}设计完整的状态管理方案：

**应用特征：**
- 应用类型：{如"电商网站"、"管理后台"}
- 复杂度：{如"中等复杂度，10+页面"}
- 团队规模：{如"5人前端团队"}

**状态类型：**
- 全局状态：{如"用户信息、主题设置"}
- 页面状态：{如"表单数据、列表筛选"}
- 缓存状态：{如"API响应缓存"}

**技术选型：**
- 状态管理库：{如"Zustand"、"Redux Toolkit"、"Jotai"}
- 数据获取：{如"React Query"、"SWR"}

**请提供：**
1. 状态结构设计
2. Store配置和中间件
3. 状态更新逻辑（actions/reducers）
4. 组件连接方式
5. 持久化方案
6. 开发工具配置
7. 最佳实践指南

**输出要求：**
- 完整的代码实现
- 架构图和数据流说明
- 使用示例和模式
- 性能优化建议
```

## 🐛 问题解决类模板

### 5. Bug调试分析
```
作为前端调试专家，请帮我分析并解决以下问题：

**问题概述：** {一句话描述问题}

**详细描述：** {详细描述问题现象和影响}

**环境信息：**
- 前端框架：{React 18.2.0}
- 构建工具：{Vite 4.0}
- 浏览器：{Chrome 118}
- 操作系统：{Windows 11}
- 其他相关：{如第三方库版本}

**重现步骤：**
1. {具体操作步骤1}
2. {具体操作步骤2}
3. {具体操作步骤3}

**期望行为：** {描述期望的正确行为}
**实际行为：** {描述实际发生的错误行为}

**相关代码：**
```javascript
{粘贴相关的代码片段}
```

**错误信息：**
```
{粘贴控制台错误信息或截图描述}
```

**已尝试的解决方案：**
- {尝试方案1及结果}
- {尝试方案2及结果}

**请提供：**
1. **根因分析**：问题的根本原因
2. **解决方案**：详细的修复步骤
3. **修复代码**：完整的修复后代码
4. **验证方法**：如何确认问题已解决
5. **预防措施**：避免类似问题的建议
6. **相关资源**：参考文档或工具推荐
```

### 6. 性能优化诊断
```
作为前端性能专家，请帮我诊断并优化以下性能问题：

**性能问题描述：** {具体的性能问题}

**当前性能数据：**
- 首屏加载时间（FCP）：{时间}
- 最大内容绘制（LCP）：{时间}
- 首次输入延迟（FID）：{时间}
- 累积布局偏移（CLS）：{数值}
- Bundle大小：{大小}

**技术栈信息：**
- 框架：{React + TypeScript}
- 构建工具：{Webpack/Vite}
- UI库：{Ant Design/Material-UI}
- 其他库：{列出主要依赖}

**页面特征：**
- 页面类型：{如"商品列表页"}
- 数据量：{如"500+商品"}
- 交互复杂度：{如"包含筛选、排序、分页"}
- 媒体资源：{如"大量图片"}

**相关代码：**
```javascript
{粘贴关键组件代码}
```

**请分析并提供：**
1. **性能瓶颈识别**：具体的性能问题点
2. **优化优先级**：按影响程度排序的优化项
3. **解决方案**：每个问题的具体优化方案
4. **代码实现**：优化后的代码示例
5. **效果预期**：预期的性能提升数据
6. **监控建议**：持续性能监控方案
7. **最佳实践**：避免性能问题的开发建议
```

### 7. 兼容性问题解决
```
作为浏览器兼容性专家，请帮我解决以下兼容性问题：

**兼容性问题：** {具体描述兼容性问题}

**目标浏览器支持：**
- Chrome：{版本要求}
- Firefox：{版本要求}
- Safari：{版本要求}
- Edge：{版本要求}
- 移动端：{iOS Safari, Android Chrome版本}
- 特殊要求：{如IE11支持}

**问题表现：**
- 在{浏览器}中：{具体表现}
- 在{浏览器}中：{具体表现}

**相关代码：**
```css/javascript
{粘贴有问题的代码}
```

**技术约束：**
- 不能使用：{限制的技术或库}
- 必须保持：{必须保持的功能特性}
- 性能要求：{性能限制}

**请提供：**
1. **兼容性分析**：各浏览器支持情况分析
2. **问题根因**：导致兼容性问题的具体原因
3. **解决方案**：
   - 主要方案（推荐）
   - 备选方案
   - 降级方案
4. **实现代码**：兼容性处理后的代码
5. **测试策略**：如何验证兼容性
6. **工具推荐**：相关的兼容性检测工具
```

## 📚 学习理解类模板

### 8. 技术概念深度学习
```
作为{技术领域}专家和技术导师，请帮我深入理解{技术概念}：

**我的技术背景：**
- 编程经验：{年限}
- 熟悉技术：{列出熟悉的技术栈}
- 当前对该概念的了解：{了解程度}

**学习目标：**
- 理解深度：{如"能够在项目中熟练应用"}
- 应用场景：{如"用于性能优化"}
- 时间要求：{如"一周内掌握"}

**重点关注：**
- {关注点1：如"工作原理"}
- {关注点2：如"最佳实践"}
- {关注点3：如"常见陷阱"}

**请按以下结构详细解释：**

1. **概念简介**
   - 用一句话定义这个概念
   - 为什么需要这个概念
   - 它解决了什么问题

2. **核心原理**
   - 底层工作机制
   - 关键算法或流程
   - 与相关概念的区别

3. **实际应用**
   - 典型使用场景
   - 在项目中的具体应用
   - 与其他技术的配合

4. **代码示例**
   - 基础使用示例
   - 进阶应用示例
   - 最佳实践代码

5. **注意事项**
   - 常见误区和陷阱
   - 性能考虑
   - 兼容性问题

6. **进阶学习**
   - 深入学习方向
   - 推荐资源和文档
   - 实践项目建议

**输出要求：**
- 语言通俗易懂，避免过度技术化
- 包含可运行的代码示例
- 提供图表或流程图的文字描述
- 给出具体的实践建议
```

### 9. 技术选型对比分析
```
作为技术架构师，请帮我进行{技术选型场景}的全面技术选型分析：

**项目背景：**
- 项目类型：{如"企业级管理系统"}
- 项目规模：{如"预计50+页面"}
- 团队情况：{如"5人前端团队，React经验2年+"}
- 开发周期：{如"6个月开发+长期维护"}
- 用户规模：{如"预计1000+并发用户"}

**候选技术方案：**
- 方案A：{技术栈A，如"React + TypeScript + Ant Design"}
- 方案B：{技术栈B，如"Vue 3 + TypeScript + Element Plus"}
- 方案C：{技术栈C，如"Next.js + TypeScript + Tailwind CSS"}

**评估维度：**
- 开发效率（权重：25%）
- 性能表现（权重：20%）
- 学习成本（权重：15%）
- 生态系统（权重：15%）
- 长期维护（权重：15%）
- 团队匹配（权重：10%）

**特殊要求：**
- {特殊需求1：如"需要支持SSR"}
- {特殊需求2：如"需要国际化支持"}
- {特殊需求3：如"需要移动端适配"}

**请提供：**

1. **详细对比分析表**
   - 各维度评分（1-10分）
   - 具体评分理由
   - 权重计算结果

2. **方案深度分析**
   - 每个方案的优势和劣势
   - 适用场景分析
   - 技术风险评估

3. **决策建议**
   - 推荐方案及详细理由
   - 决策的关键因素
   - 风险缓解措施

4. **实施计划**
   - 技术栈搭建步骤
   - 团队培训计划
   - 开发规范制定

5. **长期考虑**
   - 技术演进路径
   - 维护成本预估
   - 升级迁移策略

**输出格式：**
- 结构化的对比表格
- 决策树或评分矩阵
- 具体的行动计划
- 风险评估报告
```

### 10. 代码审查请求
```
作为资深前端架构师，请对以下代码进行全面的代码审查：

**代码背景：**
- 功能描述：{代码的具体功能}
- 开发者水平：{如"中级前端开发者"}
- 代码用途：{如"生产环境核心组件"}

**审查重点：**
- 代码架构和设计模式
- 性能优化机会
- 安全性考虑
- 可维护性和可扩展性
- 错误处理和边界情况
- 最佳实践遵循情况
- 测试覆盖度

**代码内容：**
```javascript/typescript
{粘贴完整的代码}
```

**请按以下格式提供详细的审查报告：**

1. **总体评价**
   - 代码质量评分（1-10分）
   - 总体印象和建议

2. **优点分析**
   - 做得好的地方
   - 值得学习的实践
   - 符合最佳实践的部分

3. **问题识别**
   - **严重问题**（必须修复）
     - 问题描述
     - 影响分析
     - 修复建议
   - **改进建议**（建议优化）
     - 优化点
     - 改进理由
     - 实现方案
   - **潜在风险**（需要关注）
     - 风险点
     - 可能的后果
     - 预防措施

4. **具体改进方案**
   - 重构建议（如有需要）
   - 性能优化点
   - 代码简化机会
   - 可读性改进

5. **修改示例**
   - 问题代码 vs 改进代码
   - 修改理由说明
   - 预期改进效果

6. **最佳实践建议**
   - 相关的编码规范
   - 推荐的设计模式
   - 工具和库推荐

7. **学习资源**
   - 相关文档链接
   - 推荐阅读材料
   - 进阶学习方向

**输出要求：**
- 每个问题都要有具体的代码示例
- 提供修改前后的对比
- 解释每个建议的理由和好处
- 按优先级排序所有改进项
```

## 🎯 使用技巧

### 快速定制技巧
1. **保存常用信息**：将你的技术栈、项目背景等信息保存为模板片段
2. **建立个人库**：根据你的工作特点，定制专属的提示词模板
3. **版本迭代**：根据使用效果，不断优化和改进模板内容

### 提升效果的方法
1. **具体化描述**：用具体的数据和场景替换模糊的描述
2. **分步骤执行**：复杂任务分解为多个小步骤
3. **提供上下文**：给AI足够的背景信息来理解你的需求

### 常见错误避免
1. **避免过于简单**：不要只说"帮我写个组件"
2. **避免过于复杂**：不要在一个提示词中包含太多不相关的需求
3. **避免缺少约束**：明确指定技术栈、格式要求等约束条件

这个模板库涵盖了前端开发中90%的AI协作场景，建议收藏并在实际工作中灵活使用。
