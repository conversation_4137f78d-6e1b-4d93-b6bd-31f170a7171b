# 前端AI学习项目结构与环境配置

## 📁 推荐项目结构

```
ai-learning-project/
├── docs/                          # 学习文档和笔记
│   ├── notes/                     # 学习笔记
│   ├── resources/                 # 学习资源链接
│   └── progress/                  # 学习进度记录
├── stage1-basics/                 # 第一阶段：基础概念
│   ├── ml-fundamentals/           # 机器学习基础
│   ├── nlp-basics/               # NLP基础
│   ├── cv-basics/                # 计算机视觉基础
│   └── ai-applications/          # AI应用场景研究
├── stage2-tech-stack/            # 第二阶段：技术栈学习
│   ├── tensorflow-js/            # TensorFlow.js学习
│   ├── web-ai-apis/              # Web AI API学习
│   ├── model-deployment/         # 模型部署
│   ├── ui-ux-design/             # AI驱动的UI/UX
│   └── performance/              # 性能优化
├── stage3-projects/              # 第三阶段：实践项目
│   ├── beginner/                 # 初级项目
│   │   ├── image-classifier/     # 图像分类应用
│   │   └── speech-recognition/   # 语音识别应用
│   ├── intermediate/             # 中级项目
│   │   ├── chatbot/              # 智能聊天机器人
│   │   └── recommendation/       # 推荐系统
│   └── advanced/                 # 高级项目
│       ├── object-detection/     # 目标检测应用
│       └── code-assistant/       # 代码生成助手
├── stage4-resources/             # 第四阶段：资源整合
│   ├── knowledge-base/           # 个人知识库
│   ├── blog-posts/               # 技术博客
│   └── community/                # 社区参与记录
├── tools/                        # 开发工具和脚本
├── assets/                       # 静态资源
├── package.json                  # 项目依赖
├── README.md                     # 项目说明
└── .gitignore                    # Git忽略文件
```

## 🛠️ 开发环境配置

### 1. Node.js 环境

```bash
# 检查Node.js版本（推荐v16+）
node --version

# 如果需要安装或更新Node.js
# 推荐使用nvm管理Node.js版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

### 2. 包管理器配置

```bash
# 使用npm（默认）
npm --version

# 或使用yarn
npm install -g yarn
yarn --version

# 或使用pnpm（推荐，更快更节省空间）
npm install -g pnpm
pnpm --version
```

### 3. 项目初始化

```bash
# 创建项目目录
mkdir ai-learning-project
cd ai-learning-project

# 初始化package.json
npm init -y

# 或使用pnpm
pnpm init
```

### 4. 基础依赖安装

```json
{
  "name": "ai-learning-project",
  "version": "1.0.0",
  "description": "前端AI学习项目",
  "main": "index.js",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "vitest",
    "lint": "eslint . --ext .js,.jsx,.ts,.tsx",
    "format": "prettier --write ."
  },
  "dependencies": {
    "@tensorflow/tfjs": "^4.10.0",
    "@tensorflow/tfjs-vis": "^1.5.1",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.5.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@vitejs/plugin-react": "^4.0.3",
    "eslint": "^8.45.0",
    "eslint-plugin-react": "^7.32.2",
    "prettier": "^3.0.0",
    "typescript": "^5.0.2",
    "vite": "^4.4.5",
    "vitest": "^0.34.0"
  }
}
```

### 5. VS Code 配置

创建 `.vscode/settings.json`：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  }
}
```

推荐VS Code插件：

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "github.copilot",
    "ms-python.python",
    "ms-toolsai.jupyter"
  ]
}
```

### 6. Git 配置

创建 `.gitignore`：

```gitignore
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# AI models and large files
*.h5
*.pb
*.tflite
models/
datasets/

# Temporary files
tmp/
temp/
```

### 7. 开发工具配置

#### Prettier 配置 (`.prettierrc`)

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

#### ESLint 配置 (`.eslintrc.js`)

```javascript
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: 'module',
  },
  plugins: ['react', '@typescript-eslint'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
};
```

#### Vite 配置 (`vite.config.ts`)

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
  optimizeDeps: {
    include: ['@tensorflow/tfjs'],
  },
});
```

## 🧪 测试环境配置

### Vitest 配置 (`vitest.config.ts`)

```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
});
```

### 测试设置文件 (`src/test/setup.ts`)

```typescript
import { expect, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

expect.extend(matchers);

afterEach(() => {
  cleanup();
});
```

## 📚 学习资源管理

### 1. 笔记管理

推荐使用以下工具之一：
- **Notion**: 功能强大的笔记和项目管理工具
- **Obsidian**: 基于Markdown的知识图谱工具
- **Logseq**: 开源的块式笔记工具

### 2. 代码片段管理

在VS Code中使用代码片段：

创建 `.vscode/snippets/typescript.json`：

```json
{
  "TensorFlow.js Model": {
    "prefix": "tfmodel",
    "body": [
      "import * as tf from '@tensorflow/tfjs';",
      "",
      "const model = tf.sequential({",
      "  layers: [",
      "    tf.layers.dense({ inputShape: [${1:inputSize}], units: ${2:hiddenUnits}, activation: '${3:relu}' }),",
      "    tf.layers.dense({ units: ${4:outputUnits}, activation: '${5:softmax}' })",
      "  ]",
      "});",
      "",
      "model.compile({",
      "  optimizer: '${6:adam}',",
      "  loss: '${7:categoricalCrossentropy}',",
      "  metrics: ['accuracy']",
      "});"
    ],
    "description": "Create a basic TensorFlow.js model"
  }
}
```

### 3. 项目模板

为每个学习阶段创建项目模板，快速启动新项目：

```bash
# 创建项目模板脚本
mkdir tools
touch tools/create-project.sh
chmod +x tools/create-project.sh
```

## 🚀 部署配置

### Vercel 部署

创建 `vercel.json`：

```json
{
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

### Netlify 部署

创建 `netlify.toml`：

```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

## 📊 性能监控

### Web Vitals 配置

```typescript
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric: any) {
  console.log(metric);
  // 发送到分析服务
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

这个配置为你的AI学习项目提供了完整的开发环境。你可以根据具体需求调整配置，开始你的AI学习之旅！
